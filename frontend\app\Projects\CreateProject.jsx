import React, { useState, useContext, useRef, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    TextInput,
    StyleSheet,
    StatusBar,
    SafeAreaView,
    Dimensions,
    Animated,
    Easing,
    Image,
    ActivityIndicator,
    Switch,
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { useRouter } from 'expo-router';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { useMutation } from '@tanstack/react-query';
import { createProject } from '../../api/projects/projectApi.jsx';
import Toast from 'react-native-toast-message';

const { height } = Dimensions.get('window');

const validationSchema = Yup.object().shape({
    projectName: Yup.string().required('Project name is required'),
    projectType: Yup.string().required('Project type is required'),
    constructionType: Yup.string().required('Construction type is required'),
    address: Yup.string().required('Address is required'),
    city: Yup.string().required('City is required'),
    state: Yup.string().required('State is required'),
    pincode: Yup.string().required('Pincode is required'),
    minBudget: Yup.number().positive('Minimum budget must be positive'),
    maxBudget: Yup.number()
        .positive('Maximum budget must be positive')
        .test(
            'max-greater-than-min',
            'Maximum budget must be greater than minimum budget',
            function (value) {
                const { minBudget } = this.parent;
                return !minBudget || !value || value > minBudget;
            }
        ),
    floors: Yup.number().positive('Number of floors must be positive'),
    bedrooms: Yup.number().min(0, 'Number of bedrooms cannot be negative'),
    bathrooms: Yup.number().min(0, 'Number of bathrooms cannot be negative'),
});

export default function CreateProject() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const scaleAnim = useRef(new Animated.Value(0.7)).current;
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const createProjectMutation = useMutation({
        mutationFn: createProject,
        onSuccess: () => {
            Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Project created successfully!',
            });
            router.back();
        },
        onError: (error) => {
            Toast.show({
                type: 'error',
                text1: 'Error',
                text2: error.message || 'Failed to create project',
            });
        },
    });

    // Initialize animations
    useEffect(() => {
        Animated.parallel([
            Animated.spring(scaleAnim, {
                toValue: 1,
                friction: 7,
                tension: 60,
                useNativeDriver: true,
            }),
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 600,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    // Form options
    const projectTypes = ['Residential', 'Commercial', 'Industrial', 'Other'];
    const constructionTypes = ['New Construction', 'Renovation', 'Extension'];

    // Initial form values
    const initialValues = {
        projectName: '',
        projectType: '',
        constructionType: '',
        address: '',
        city: '',
        state: '',
        pincode: '',
        expectedStartDate: '',
        expectedCompletionDate: '',
        minBudget: '',
        maxBudget: '',
        floors: '',
        bedrooms: '',
        bathrooms: '',
        parkingRequired: false,
        gardenRequired: false,
        vastuCompliance: false,
        brokerAssistanceRequired: false,
        specialInstructions: '',
        additionalFacilities: '',
    };

    // Handle form submission
    const handleSubmit = (values) => {
        const projectData = {
            projectName: values.projectName,
            projectType: values.projectType,
            constructionType: values.constructionType,
            location: {
                address: values.address,
                city: values.city,
                state: values.state,
                pincode: values.pincode,
            },
            expectedStartDate: values.expectedStartDate || undefined,
            expectedCompletionDate: values.expectedCompletionDate || undefined,
            budget: {
                minBudget: values.minBudget
                    ? parseInt(values.minBudget)
                    : undefined,
                maxBudget: values.maxBudget
                    ? parseInt(values.maxBudget)
                    : undefined,
            },
            designPreferences: {
                floors: values.floors ? parseInt(values.floors) : undefined,
                bedrooms: values.bedrooms
                    ? parseInt(values.bedrooms)
                    : undefined,
                bathrooms: values.bathrooms
                    ? parseInt(values.bathrooms)
                    : undefined,
                parkingRequired: values.parkingRequired,
                gardenRequired: values.gardenRequired,
                vastuCompliance: values.vastuCompliance,
            },
            brokerAssistanceRequired: values.brokerAssistanceRequired,
            specialInstructions: values.specialInstructions || undefined,
            additionalFacilities: values.additionalFacilities
                ? values.additionalFacilities
                      .split(',')
                      .map((f) => f.trim())
                      .filter((f) => f)
                : [],
        };

        createProjectMutation.mutate(projectData);
    };

    return (
        <View style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
            />

            {/* Background Pattern */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                    style={styles.backgroundOverlay}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                />
            </View>

            <SafeAreaView style={styles.safeArea}>
                {/* Header */}
                <View style={styles.headerContent}>
                    <BackButton color={theme.WHITE} />
                </View>
                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                >
                    {({
                        values,
                        errors,
                        touched,
                        handleChange,
                        handleBlur,
                        handleSubmit,
                        setFieldValue,
                        setFieldTouched,
                    }) => (
                        <ScrollView
                            style={styles.scrollView}
                            showsVerticalScrollIndicator={false}
                        >
                            <Animated.View
                                style={[
                                    styles.formContainer,
                                    {
                                        transform: [{ scale: scaleAnim }],
                                        opacity: fadeAnim,
                                        shadowColor: theme.SHADOW,
                                        backgroundColor: theme.CARD,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.title,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Create Project
                                </Text>
                                <Text
                                    style={[
                                        styles.subtitle,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Submit your project preferences to get
                                    matched with the right brokers and
                                    contractors.
                                </Text>
                                {/* Project Name */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Project name
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor: theme.CARD,
                                                borderColor:
                                                    errors.projectName &&
                                                    touched.projectName
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.projectName &&
                                                touched.projectName &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="home-outline"
                                            size={20}
                                            color={
                                                errors.projectName &&
                                                touched.projectName
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    flex: 1,
                                                },
                                            ]}
                                            placeholder="Enter project name"
                                            placeholderTextColor={
                                                theme.TEXT_PLACEHOLDER
                                            }
                                            value={values.projectName}
                                            onChangeText={handleChange(
                                                'projectName'
                                            )}
                                            onBlur={handleBlur('projectName')}
                                        />
                                    </View>
                                    {errors.projectName &&
                                        touched.projectName && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.projectName}
                                            </Text>
                                        )}
                                </View>

                                {/* Project Type */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Project type
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor: theme.CARD,
                                                borderColor:
                                                    errors.projectType &&
                                                    touched.projectType
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.projectType &&
                                                touched.projectType &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="business-outline"
                                            size={20}
                                            color={
                                                errors.projectType &&
                                                touched.projectType
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <Picker
                                            selectedValue={values.projectType}
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'projectType',
                                                    value
                                                )
                                            }
                                            onBlur={() =>
                                                setFieldTouched(
                                                    'projectType',
                                                    true
                                                )
                                            }
                                            dropdownIconColor={theme.PRIMARY}
                                            style={[
                                                styles.picker,
                                                {
                                                    color: values.projectType
                                                        ? theme.TEXT_PRIMARY
                                                        : theme.TEXT_PLACEHOLDER,
                                                    flex: 1,
                                                },
                                            ]}
                                            accessibilityLabel="Select project type"
                                            testID="project-type-picker"
                                        >
                                            <Picker.Item
                                                label="Select Project Type"
                                                value=""
                                            />
                                            {projectTypes.map((type) => (
                                                <Picker.Item
                                                    key={type}
                                                    label={type}
                                                    value={type}
                                                />
                                            ))}
                                        </Picker>
                                    </View>
                                    {errors.projectType &&
                                        touched.projectType && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.projectType}
                                            </Text>
                                        )}
                                </View>
                                {/* Construction Type */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Construction type
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>

                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor: theme.CARD,
                                                borderColor:
                                                    errors.constructionType &&
                                                    touched.constructionType
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.constructionType &&
                                                touched.constructionType &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="construct-outline"
                                            size={20}
                                            color={
                                                errors.constructionType &&
                                                touched.constructionType
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <Picker
                                            selectedValue={
                                                values.constructionType
                                            }
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'constructionType',
                                                    value
                                                )
                                            }
                                            onBlur={() =>
                                                setFieldTouched(
                                                    'constructionType',
                                                    true
                                                )
                                            }
                                            dropdownIconColor={theme.PRIMARY}
                                            style={[
                                                styles.picker,
                                                {
                                                    color: values.constructionType
                                                        ? theme.TEXT_PRIMARY
                                                        : theme.TEXT_PLACEHOLDER,
                                                    flex: 1,
                                                },
                                            ]}
                                            accessibilityLabel="Select construction type"
                                            testID="construction-type-picker"
                                        >
                                            <Picker.Item
                                                label="Select Construction Type"
                                                value=""
                                            />
                                            {constructionTypes.map((type) => (
                                                <Picker.Item
                                                    key={type}
                                                    label={type}
                                                    value={type}
                                                />
                                            ))}
                                        </Picker>
                                    </View>
                                    {errors.constructionType &&
                                        touched.constructionType && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.constructionType}
                                            </Text>
                                        )}
                                </View>
                                {/* Location Details Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="location-on"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Location Details
                                    </Text>
                                </View>

                                {/* Address */}
                                <Text
                                    style={[
                                        styles.label,
                                        { color: theme.TEXT_PRIMARY },
                                    ]}
                                >
                                    Address
                                    <Text style={{ color: theme.ERROR }}>
                                        *
                                    </Text>
                                </Text>
                                <View
                                    style={[
                                        styles.inputContainer,
                                        styles.addressInputContainer,
                                        {
                                            backgroundColor:
                                                theme.INPUT_BACKGROUND,
                                            borderColor:
                                                errors.address &&
                                                touched.address
                                                    ? theme.ERROR
                                                    : theme.INPUT_BORDER,
                                        },
                                        errors.address &&
                                            touched.address &&
                                            styles.inputError,
                                    ]}
                                >
                                    <Ionicons
                                        name="location-outline"
                                        size={22}
                                        color={
                                            errors.address && touched.address
                                                ? theme.ERROR
                                                : theme.PRIMARY
                                        }
                                        style={styles.inputIcon}
                                    />
                                    <TextInput
                                        placeholder="Enter full address"
                                        value={values.address}
                                        onChangeText={handleChange('address')}
                                        onBlur={handleBlur('address')}
                                        placeholderTextColor={
                                            theme.TEXT_PLACEHOLDER
                                        }
                                        style={[
                                            styles.input,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                        multiline
                                        accessibilityLabel="Enter address"
                                        testID="address-input"
                                    />
                                    {values.address.length > 0 && (
                                        <TouchableOpacity
                                            onPress={() =>
                                                setFieldValue('address', '')
                                            }
                                            style={styles.clearButton}
                                        >
                                            <Ionicons
                                                name="close-circle"
                                                size={20}
                                                color="#999"
                                            />
                                        </TouchableOpacity>
                                    )}
                                </View>
                                {errors.address && touched.address && (
                                    <Text
                                        style={[
                                            styles.errorText,
                                            { color: theme.ERROR },
                                        ]}
                                    >
                                        {errors.address}
                                    </Text>
                                )}
                                {/* City */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        City
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                borderColor:
                                                    errors.city && touched.city
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.city &&
                                                touched.city &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="business-outline"
                                            size={20}
                                            color={
                                                errors.city && touched.city
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    flex: 1,
                                                },
                                            ]}
                                            placeholder="Enter city"
                                            placeholderTextColor={
                                                theme.TEXT_PLACEHOLDER
                                            }
                                            value={values.city}
                                            onChangeText={handleChange('city')}
                                            onBlur={handleBlur('city')}
                                        />
                                    </View>
                                    {errors.city && touched.city && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.city}
                                        </Text>
                                    )}
                                </View>

                                {/* State */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        State
                                        <Text style={{ color: theme.ERROR }}>
                                            *
                                        </Text>
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                borderColor:
                                                    errors.state &&
                                                    touched.state
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.state &&
                                                touched.state &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="map-outline"
                                            size={20}
                                            color={
                                                errors.state && touched.state
                                                    ? theme.ERROR
                                                    : theme.PRIMARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    flex: 1,
                                                },
                                            ]}
                                            placeholder="Enter state"
                                            placeholderTextColor={
                                                theme.TEXT_PLACEHOLDER
                                            }
                                            value={values.state}
                                            onChangeText={handleChange('state')}
                                            onBlur={handleBlur('state')}
                                        />
                                    </View>
                                    {errors.state && touched.state && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.state}
                                        </Text>
                                    )}
                                </View>
                                {/* Pincode */}
                                <View style={styles.inputGroup}>
                                    <Text
                                        style={[
                                            styles.label,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Pincode *
                                    </Text>
                                    <View
                                        style={[
                                            styles.inputContainer,
                                            {
                                                backgroundColor: theme.CARD,
                                                borderColor:
                                                    errors.pincode &&
                                                    touched.pincode
                                                        ? theme.ERROR
                                                        : theme.INPUT_BORDER,
                                            },
                                            errors.pincode &&
                                                touched.pincode &&
                                                styles.inputError,
                                        ]}
                                    >
                                        <Ionicons
                                            name="mail-outline"
                                            size={20}
                                            color={
                                                errors.pincode &&
                                                touched.pincode
                                                    ? theme.ERROR
                                                    : theme.TEXT_SECONDARY
                                            }
                                            style={styles.inputIcon}
                                        />
                                        <TextInput
                                            style={[
                                                styles.input,
                                                {
                                                    color: theme.TEXT_PRIMARY,
                                                    flex: 1,
                                                },
                                            ]}
                                            placeholder="Enter pincode"
                                            placeholderTextColor={
                                                theme.TEXT_PLACEHOLDER
                                            }
                                            value={values.pincode}
                                            onChangeText={handleChange(
                                                'pincode'
                                            )}
                                            onBlur={handleBlur('pincode')}
                                            keyboardType="numeric"
                                            maxLength={6}
                                        />
                                    </View>
                                    {errors.pincode && touched.pincode && (
                                        <Text
                                            style={[
                                                styles.errorText,
                                                { color: theme.ERROR },
                                            ]}
                                        >
                                            {errors.pincode}
                                        </Text>
                                    )}
                                </View>

                                {/* Project Timeline Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="schedule"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Project Timeline
                                    </Text>
                                </View>

                                {/* Expected Start Date and Completion Date Row */}
                                <View style={styles.row}>
                                    {/* Expected Start Date */}
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Expected Start Date
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    borderColor:
                                                        errors.expectedStartDate &&
                                                        touched.expectedStartDate
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.expectedStartDate &&
                                                    touched.expectedStartDate &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="calendar-outline"
                                                size={20}
                                                color={
                                                    errors.expectedStartDate &&
                                                    touched.expectedStartDate
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="DD/MM/YYYY"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.expectedStartDate}
                                                onChangeText={handleChange(
                                                    'expectedStartDate'
                                                )}
                                                onBlur={handleBlur(
                                                    'expectedStartDate'
                                                )}
                                            />
                                        </View>
                                        {errors.expectedStartDate &&
                                            touched.expectedStartDate && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.expectedStartDate}
                                                </Text>
                                            )}
                                    </View>

                                    {/* Expected Completion Date */}
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Expected Completion Date
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    borderColor:
                                                        errors.expectedCompletionDate &&
                                                        touched.expectedCompletionDate
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.expectedCompletionDate &&
                                                    touched.expectedCompletionDate &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="calendar-outline"
                                                size={20}
                                                color={
                                                    errors.expectedCompletionDate &&
                                                    touched.expectedCompletionDate
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="DD/MM/YYYY"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={
                                                    values.expectedCompletionDate
                                                }
                                                onChangeText={handleChange(
                                                    'expectedCompletionDate'
                                                )}
                                                onBlur={handleBlur(
                                                    'expectedCompletionDate'
                                                )}
                                            />
                                        </View>
                                        {errors.expectedCompletionDate &&
                                            touched.expectedCompletionDate && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {
                                                        errors.expectedCompletionDate
                                                    }
                                                </Text>
                                            )}
                                    </View>
                                </View>

                                {/* Budget Details Section */}
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="attach-money"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Budget Details
                                    </Text>
                                </View>

                                {/* Budget Range Row */}
                                <View style={styles.row}>
                                    {/* Min Budget */}
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Min Budget (₹)
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    borderColor:
                                                        errors.minBudget &&
                                                        touched.minBudget
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.minBudget &&
                                                    touched.minBudget &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="cash-outline"
                                                size={20}
                                                color={
                                                    errors.minBudget &&
                                                    touched.minBudget
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Enter minimum budget"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.minBudget}
                                                onChangeText={handleChange(
                                                    'minBudget'
                                                )}
                                                onBlur={handleBlur('minBudget')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.minBudget &&
                                            touched.minBudget && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.minBudget}
                                                </Text>
                                            )}
                                    </View>

                                    {/* Max Budget */}
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 8 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Max Budget (₹)
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    borderColor:
                                                        errors.maxBudget &&
                                                        touched.maxBudget
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.maxBudget &&
                                                    touched.maxBudget &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="wallet-outline"
                                                size={20}
                                                color={
                                                    errors.maxBudget &&
                                                    touched.maxBudget
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Enter maximum budget"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.maxBudget}
                                                onChangeText={handleChange(
                                                    'maxBudget'
                                                )}
                                                onBlur={handleBlur('maxBudget')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.maxBudget &&
                                            touched.maxBudget && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.maxBudget}
                                                </Text>
                                            )}
                                    </View>
                                </View>
                                <View style={styles.sectionHeader}>
                                    <MaterialIcons
                                        name="home"
                                        size={20}
                                        color={theme.PRIMARY}
                                    />
                                    <Text
                                        style={[
                                            styles.sectionTitle,
                                            { color: theme.TEXT_PRIMARY },
                                        ]}
                                    >
                                        Design Preferences
                                    </Text>
                                </View>
                                <View style={styles.row}>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginRight: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Floors
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    borderColor:
                                                        errors.floors &&
                                                        touched.floors
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.floors &&
                                                    touched.floors &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="layers-outline"
                                                size={20}
                                                color={
                                                    errors.floors &&
                                                    touched.floors
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Number of floors"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.floors}
                                                onChangeText={handleChange(
                                                    'floors'
                                                )}
                                                onBlur={handleBlur('floors')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.floors && touched.floors && (
                                            <Text
                                                style={[
                                                    styles.errorText,
                                                    { color: theme.ERROR },
                                                ]}
                                            >
                                                {errors.floors}
                                            </Text>
                                        )}
                                    </View>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginHorizontal: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Bedrooms
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    borderColor:
                                                        errors.bedrooms &&
                                                        touched.bedrooms
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.bedrooms &&
                                                    touched.bedrooms &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <Ionicons
                                                name="bed-outline"
                                                size={20}
                                                color={
                                                    errors.bedrooms &&
                                                    touched.bedrooms
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Number of bedrooms"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.bedrooms}
                                                onChangeText={handleChange(
                                                    'bedrooms'
                                                )}
                                                onBlur={handleBlur('bedrooms')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.bedrooms &&
                                            touched.bedrooms && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.bedrooms}
                                                </Text>
                                            )}
                                    </View>
                                    <View
                                        style={[
                                            styles.inputGroup,
                                            { flex: 1, marginLeft: 4 },
                                        ]}
                                    >
                                        <Text
                                            style={[
                                                styles.label,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Bathrooms
                                        </Text>
                                        <View
                                            style={[
                                                styles.inputContainer,
                                                {
                                                    backgroundColor: theme.CARD,
                                                    borderColor:
                                                        errors.bathrooms &&
                                                        touched.bathrooms
                                                            ? theme.ERROR
                                                            : theme.INPUT_BORDER,
                                                },
                                                errors.bathrooms &&
                                                    touched.bathrooms &&
                                                    styles.inputError,
                                            ]}
                                        >
                                            <MaterialIcons
                                                name="bathroom"
                                                size={20}
                                                color={
                                                    errors.bathrooms &&
                                                    touched.bathrooms
                                                        ? theme.ERROR
                                                        : theme.TEXT_SECONDARY
                                                }
                                                style={styles.inputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.input,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                        flex: 1,
                                                    },
                                                ]}
                                                placeholder="Number of bathrooms"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={values.bathrooms}
                                                onChangeText={handleChange(
                                                    'bathrooms'
                                                )}
                                                onBlur={handleBlur('bathrooms')}
                                                keyboardType="numeric"
                                            />
                                        </View>
                                        {errors.bathrooms &&
                                            touched.bathrooms && (
                                                <Text
                                                    style={[
                                                        styles.errorText,
                                                        { color: theme.ERROR },
                                                    ]}
                                                >
                                                    {errors.bathrooms}
                                                </Text>
                                            )}
                                    </View>
                                </View>

                                {/* Preferences Switches */}
                                <View style={styles.switchContainer}>
                                    <View style={styles.switchRow}>
                                        <View
                                            style={styles.switchLabelContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.switchLabel,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Parking Required
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.switchDescription,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Include dedicated parking space
                                            </Text>
                                        </View>
                                        <Switch
                                            value={values.parkingRequired}
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'parkingRequired',
                                                    value
                                                )
                                            }
                                            trackColor={{
                                                false: theme.INPUT_BORDER,
                                                true: theme.PRIMARY,
                                            }}
                                            thumbColor={
                                                values.parkingRequired
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY
                                            }
                                        />
                                    </View>

                                    <View style={styles.switchRow}>
                                        <View
                                            style={styles.switchLabelContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.switchLabel,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Garden Required
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.switchDescription,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Include garden or landscaping
                                                area
                                            </Text>
                                        </View>
                                        <Switch
                                            value={values.gardenRequired}
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'gardenRequired',
                                                    value
                                                )
                                            }
                                            trackColor={{
                                                false: theme.INPUT_BORDER,
                                                true: theme.PRIMARY,
                                            }}
                                            thumbColor={
                                                values.gardenRequired
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY
                                            }
                                        />
                                    </View>

                                    <View style={styles.switchRow}>
                                        <View
                                            style={styles.switchLabelContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.switchLabel,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Vastu Compliance
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.switchDescription,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Follow traditional Vastu
                                                principles
                                            </Text>
                                        </View>
                                        <Switch
                                            value={values.vastuCompliance}
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'vastuCompliance',
                                                    value
                                                )
                                            }
                                            trackColor={{
                                                false: theme.INPUT_BORDER,
                                                true: theme.PRIMARY,
                                            }}
                                            thumbColor={
                                                values.vastuCompliance
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY
                                            }
                                        />
                                    </View>

                                    <View style={styles.switchRow}>
                                        <View
                                            style={styles.switchLabelContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.switchLabel,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Broker Assistance Required
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.switchDescription,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Get help from real estate
                                                brokers
                                            </Text>
                                        </View>
                                        <Switch
                                            value={
                                                values.brokerAssistanceRequired
                                            }
                                            onValueChange={(value) =>
                                                setFieldValue(
                                                    'brokerAssistanceRequired',
                                                    value
                                                )
                                            }
                                            trackColor={{
                                                false: theme.INPUT_BORDER,
                                                true: theme.PRIMARY,
                                            }}
                                            thumbColor={
                                                values.brokerAssistanceRequired
                                                    ? theme.WHITE
                                                    : theme.TEXT_SECONDARY
                                            }
                                        />
                                    </View>
                                </View>

                                {/* Additional Details Section - Modern Design */}
                                <View
                                    style={[
                                        styles.modernSectionContainer,
                                        { backgroundColor: theme.CARD + '40' },
                                    ]}
                                >
                                    <View style={styles.modernSectionHeader}>
                                        <View
                                            style={[
                                                styles.iconContainer,
                                                {
                                                    backgroundColor:
                                                        theme.PRIMARY + '15',
                                                },
                                            ]}
                                        >
                                            <MaterialIcons
                                                name="auto-awesome"
                                                size={24}
                                                color={theme.PRIMARY}
                                            />
                                        </View>
                                        <View
                                            style={styles.headerTextContainer}
                                        >
                                            <Text
                                                style={[
                                                    styles.modernSectionTitle,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                            >
                                                Personalize Your Project
                                            </Text>
                                            <Text
                                                style={[
                                                    styles.modernSectionSubtitle,
                                                    {
                                                        color: theme.TEXT_SECONDARY,
                                                    },
                                                ]}
                                            >
                                                Add the finishing touches that
                                                make it uniquely yours
                                            </Text>
                                        </View>
                                    </View>

                                    {/* Progress Indicator */}
                                    <View style={styles.progressContainer}>
                                        <View style={styles.progressTrack}>
                                            <View
                                                style={[
                                                    styles.progressFill,
                                                    {
                                                        backgroundColor:
                                                            theme.PRIMARY,
                                                        width: `${Math.min(100, ((values.additionalFacilities.length + values.specialInstructions.length) / 20) * 100)}%`,
                                                    },
                                                ]}
                                            />
                                        </View>
                                        <Text
                                            style={[
                                                styles.progressText,
                                                { color: theme.TEXT_SECONDARY },
                                            ]}
                                        >
                                            {values.additionalFacilities
                                                .length +
                                                values.specialInstructions
                                                    .length >
                                            0
                                                ? 'Great! Adding details helps us match you better'
                                                : 'Optional but recommended for better matches'}
                                        </Text>
                                    </View>
                                </View>
                                {/* Modern Facilities Selector */}
                                <View
                                    style={[
                                        styles.modernCard,
                                        { backgroundColor: theme.CARD },
                                    ]}
                                >
                                    <View style={styles.cardHeader}>
                                        <View style={styles.cardHeaderLeft}>
                                            <View
                                                style={[
                                                    styles.cardIcon,
                                                    {
                                                        backgroundColor:
                                                            theme.PRIMARY +
                                                            '15',
                                                    },
                                                ]}
                                            >
                                                <MaterialIcons
                                                    name="home-work"
                                                    size={20}
                                                    color={theme.PRIMARY}
                                                />
                                            </View>
                                            <View>
                                                <Text
                                                    style={[
                                                        styles.cardTitle,
                                                        {
                                                            color: theme.TEXT_PRIMARY,
                                                        },
                                                    ]}
                                                >
                                                    Dream Amenities
                                                </Text>
                                                <Text
                                                    style={[
                                                        styles.cardSubtitle,
                                                        {
                                                            color: theme.TEXT_SECONDARY,
                                                        },
                                                    ]}
                                                >
                                                    What would make your space
                                                    perfect?
                                                </Text>
                                            </View>
                                        </View>
                                        <View
                                            style={[
                                                styles.badge,
                                                {
                                                    backgroundColor:
                                                        theme.PRIMARY + '20',
                                                },
                                            ]}
                                        >
                                            <Text
                                                style={[
                                                    styles.badgeText,
                                                    { color: theme.PRIMARY },
                                                ]}
                                            >
                                                {
                                                    values.additionalFacilities
                                                        .split(',')
                                                        .filter((f) => f.trim())
                                                        .length
                                                }
                                            </Text>
                                        </View>
                                    </View>

                                    {/* Smart Facility Grid */}
                                    <View style={styles.facilitiesGrid}>
                                        {[
                                            {
                                                name: 'Swimming Pool',
                                                icon: 'pool',
                                                category: 'Recreation',
                                            },
                                            {
                                                name: 'Gym',
                                                icon: 'fitness-center',
                                                category: 'Fitness',
                                            },
                                            {
                                                name: 'Garden',
                                                icon: 'local-florist',
                                                category: 'Nature',
                                            },
                                            {
                                                name: 'Parking',
                                                icon: 'local-parking',
                                                category: 'Essential',
                                            },
                                            {
                                                name: 'Security',
                                                icon: 'security',
                                                category: 'Safety',
                                            },
                                            {
                                                name: 'Elevator',
                                                icon: 'elevator',
                                                category: 'Accessibility',
                                            },
                                            {
                                                name: 'Balcony',
                                                icon: 'balcony',
                                                category: 'Space',
                                            },
                                            {
                                                name: 'Terrace',
                                                icon: 'deck',
                                                category: 'Outdoor',
                                            },
                                            {
                                                name: 'Study Room',
                                                icon: 'menu-book',
                                                category: 'Work',
                                            },
                                            {
                                                name: 'Prayer Room',
                                                icon: 'self-improvement',
                                                category: 'Spiritual',
                                            },
                                            {
                                                name: 'Kids Play Area',
                                                icon: 'child-care',
                                                category: 'Family',
                                            },
                                            {
                                                name: 'Solar Panels',
                                                icon: 'solar-power',
                                                category: 'Eco',
                                            },
                                        ].map((facility) => {
                                            const isSelected =
                                                values.additionalFacilities
                                                    .toLowerCase()
                                                    .includes(
                                                        facility.name.toLowerCase()
                                                    );

                                            return (
                                                <TouchableOpacity
                                                    key={facility.name}
                                                    style={[
                                                        styles.facilityCard,
                                                        {
                                                            backgroundColor:
                                                                isSelected
                                                                    ? theme.PRIMARY +
                                                                      '15'
                                                                    : theme.BACKGROUND,
                                                            borderColor:
                                                                isSelected
                                                                    ? theme.PRIMARY
                                                                    : theme.INPUT_BORDER,
                                                            borderWidth:
                                                                isSelected
                                                                    ? 2
                                                                    : 1,
                                                        },
                                                    ]}
                                                    onPress={() => {
                                                        const currentFacilities =
                                                            values.additionalFacilities;
                                                        const facilityList =
                                                            currentFacilities
                                                                .split(',')
                                                                .map((f) =>
                                                                    f.trim()
                                                                )
                                                                .filter(
                                                                    (f) => f
                                                                );

                                                        if (isSelected) {
                                                            // Remove facility
                                                            const newList =
                                                                facilityList.filter(
                                                                    (f) =>
                                                                        f.toLowerCase() !==
                                                                        facility.name.toLowerCase()
                                                                );
                                                            setFieldValue(
                                                                'additionalFacilities',
                                                                newList.join(
                                                                    ', '
                                                                )
                                                            );
                                                        } else {
                                                            // Add facility
                                                            const newList = [
                                                                ...facilityList,
                                                                facility.name,
                                                            ];
                                                            setFieldValue(
                                                                'additionalFacilities',
                                                                newList.join(
                                                                    ', '
                                                                )
                                                            );
                                                        }
                                                    }}
                                                    activeOpacity={0.7}
                                                >
                                                    <View
                                                        style={[
                                                            styles.facilityIconContainer,
                                                            {
                                                                backgroundColor:
                                                                    isSelected
                                                                        ? theme.PRIMARY
                                                                        : theme.TEXT_SECONDARY +
                                                                          '20',
                                                            },
                                                        ]}
                                                    >
                                                        <MaterialIcons
                                                            name={facility.icon}
                                                            size={18}
                                                            color={
                                                                isSelected
                                                                    ? theme.WHITE
                                                                    : theme.TEXT_SECONDARY
                                                            }
                                                        />
                                                    </View>
                                                    <Text
                                                        style={[
                                                            styles.facilityName,
                                                            {
                                                                color: isSelected
                                                                    ? theme.PRIMARY
                                                                    : theme.TEXT_PRIMARY,
                                                            },
                                                        ]}
                                                    >
                                                        {facility.name}
                                                    </Text>
                                                    <Text
                                                        style={[
                                                            styles.facilityCategory,
                                                            {
                                                                color: theme.TEXT_SECONDARY,
                                                            },
                                                        ]}
                                                    >
                                                        {facility.category}
                                                    </Text>
                                                    {isSelected && (
                                                        <View
                                                            style={[
                                                                styles.checkmark,
                                                                {
                                                                    backgroundColor:
                                                                        theme.PRIMARY,
                                                                },
                                                            ]}
                                                        >
                                                            <Ionicons
                                                                name="checkmark"
                                                                size={12}
                                                                color={
                                                                    theme.WHITE
                                                                }
                                                            />
                                                        </View>
                                                    )}
                                                </TouchableOpacity>
                                            );
                                        })}
                                    </View>

                                    {/* Custom Input for Additional Facilities */}
                                    <View style={styles.customInputSection}>
                                        <Text
                                            style={[
                                                styles.customInputLabel,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Something else in mind?
                                        </Text>
                                        <View
                                            style={[
                                                styles.customInputContainer,
                                                {
                                                    backgroundColor:
                                                        theme.BACKGROUND,
                                                    borderColor:
                                                        theme.INPUT_BORDER,
                                                },
                                            ]}
                                        >
                                            <Ionicons
                                                name="add-circle-outline"
                                                size={20}
                                                color={theme.TEXT_SECONDARY}
                                                style={styles.customInputIcon}
                                            />
                                            <TextInput
                                                style={[
                                                    styles.customInput,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                                placeholder="Type custom amenities (comma separated)"
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={
                                                    values.additionalFacilities
                                                }
                                                onChangeText={handleChange(
                                                    'additionalFacilities'
                                                )}
                                                onBlur={handleBlur(
                                                    'additionalFacilities'
                                                )}
                                                multiline
                                                maxLength={500}
                                            />
                                        </View>
                                        <Text
                                            style={[
                                                styles.characterCount,
                                                { color: theme.TEXT_SECONDARY },
                                            ]}
                                        >
                                            {values.additionalFacilities.length}
                                            /500 characters
                                        </Text>
                                    </View>
                                </View>
                                {/* Modern Special Instructions */}
                                <View
                                    style={[
                                        styles.modernCard,
                                        { backgroundColor: theme.CARD },
                                    ]}
                                >
                                    <View style={styles.cardHeader}>
                                        <View style={styles.cardHeaderLeft}>
                                            <View
                                                style={[
                                                    styles.cardIcon,
                                                    {
                                                        backgroundColor:
                                                            theme.PRIMARY +
                                                            '15',
                                                    },
                                                ]}
                                            >
                                                <MaterialIcons
                                                    name="psychology"
                                                    size={20}
                                                    color={theme.PRIMARY}
                                                />
                                            </View>
                                            <View>
                                                <Text
                                                    style={[
                                                        styles.cardTitle,
                                                        {
                                                            color: theme.TEXT_PRIMARY,
                                                        },
                                                    ]}
                                                >
                                                    Special Requirements
                                                </Text>
                                                <Text
                                                    style={[
                                                        styles.cardSubtitle,
                                                        {
                                                            color: theme.TEXT_SECONDARY,
                                                        },
                                                    ]}
                                                >
                                                    Tell us what makes your
                                                    project unique
                                                </Text>
                                            </View>
                                        </View>
                                        <View
                                            style={[
                                                styles.badge,
                                                {
                                                    backgroundColor:
                                                        theme.PRIMARY + '20',
                                                },
                                            ]}
                                        >
                                            <Text
                                                style={[
                                                    styles.badgeText,
                                                    { color: theme.PRIMARY },
                                                ]}
                                            >
                                                {values.specialInstructions
                                                    .length > 0
                                                    ? '✓'
                                                    : '?'}
                                            </Text>
                                        </View>
                                    </View>

                                    {/* Smart Prompts */}
                                    <View style={styles.promptsContainer}>
                                        <Text
                                            style={[
                                                styles.promptsTitle,
                                                { color: theme.TEXT_PRIMARY },
                                            ]}
                                        >
                                            Quick prompts to get you started:
                                        </Text>
                                        <View style={styles.promptsGrid}>
                                            {[
                                                {
                                                    icon: 'accessible',
                                                    text: 'Accessibility needs',
                                                    prompt: 'I need wheelchair accessibility with ramps and wider doorways.',
                                                },
                                                {
                                                    icon: 'eco',
                                                    text: 'Eco-friendly',
                                                    prompt: 'I prefer sustainable materials and energy-efficient solutions.',
                                                },
                                                {
                                                    icon: 'palette',
                                                    text: 'Design style',
                                                    prompt: 'I love modern minimalist design with neutral colors.',
                                                },
                                                {
                                                    icon: 'schedule',
                                                    text: 'Timeline',
                                                    prompt: 'I have flexibility with the timeline and can adjust as needed.',
                                                },
                                                {
                                                    icon: 'account-balance',
                                                    text: 'Budget notes',
                                                    prompt: 'I have some budget constraints and would like cost-effective options.',
                                                },
                                                {
                                                    icon: 'location-city',
                                                    text: 'Local rules',
                                                    prompt: 'Please consider local building codes and neighborhood guidelines.',
                                                },
                                            ].map((prompt, index) => (
                                                <TouchableOpacity
                                                    key={index}
                                                    style={[
                                                        styles.promptCard,
                                                        {
                                                            backgroundColor:
                                                                theme.BACKGROUND,
                                                            borderColor:
                                                                theme.INPUT_BORDER,
                                                        },
                                                    ]}
                                                    onPress={() => {
                                                        const currentText =
                                                            values.specialInstructions;
                                                        const newText =
                                                            currentText
                                                                ? `${currentText}\n\n${prompt.prompt}`
                                                                : prompt.prompt;
                                                        setFieldValue(
                                                            'specialInstructions',
                                                            newText
                                                        );
                                                    }}
                                                    activeOpacity={0.7}
                                                >
                                                    <MaterialIcons
                                                        name={prompt.icon}
                                                        size={16}
                                                        color={theme.PRIMARY}
                                                    />
                                                    <Text
                                                        style={[
                                                            styles.promptText,
                                                            {
                                                                color: theme.TEXT_PRIMARY,
                                                            },
                                                        ]}
                                                    >
                                                        {prompt.text}
                                                    </Text>
                                                </TouchableOpacity>
                                            ))}
                                        </View>
                                    </View>

                                    {/* Enhanced Text Area */}
                                    <View style={styles.textAreaSection}>
                                        <View
                                            style={[
                                                styles.enhancedTextArea,
                                                {
                                                    backgroundColor:
                                                        theme.BACKGROUND,
                                                    borderColor:
                                                        values
                                                            .specialInstructions
                                                            .length > 0
                                                            ? theme.PRIMARY
                                                            : theme.INPUT_BORDER,
                                                    borderWidth:
                                                        values
                                                            .specialInstructions
                                                            .length > 0
                                                            ? 2
                                                            : 1,
                                                },
                                            ]}
                                        >
                                            <View style={styles.textAreaHeader}>
                                                <MaterialIcons
                                                    name="edit-note"
                                                    size={18}
                                                    color={theme.TEXT_SECONDARY}
                                                />
                                                <Text
                                                    style={[
                                                        styles.textAreaLabel,
                                                        {
                                                            color: theme.TEXT_SECONDARY,
                                                        },
                                                    ]}
                                                >
                                                    Share your thoughts...
                                                </Text>
                                                <Text
                                                    style={[
                                                        styles.characterCount,
                                                        {
                                                            color: theme.TEXT_SECONDARY,
                                                        },
                                                    ]}
                                                >
                                                    {
                                                        values
                                                            .specialInstructions
                                                            .length
                                                    }
                                                    /1000
                                                </Text>
                                            </View>
                                            <TextInput
                                                style={[
                                                    styles.enhancedInput,
                                                    {
                                                        color: theme.TEXT_PRIMARY,
                                                    },
                                                ]}
                                                placeholder="Describe any specific requirements, preferences, or constraints for your project. The more details you provide, the better we can match you with the right professionals."
                                                placeholderTextColor={
                                                    theme.TEXT_PLACEHOLDER
                                                }
                                                value={
                                                    values.specialInstructions
                                                }
                                                onChangeText={handleChange(
                                                    'specialInstructions'
                                                )}
                                                onBlur={handleBlur(
                                                    'specialInstructions'
                                                )}
                                                multiline
                                                numberOfLines={6}
                                                maxLength={1000}
                                                textAlignVertical="top"
                                            />
                                        </View>

                                        {/* Smart Suggestions */}
                                        {values.specialInstructions.length >
                                            0 && (
                                            <View
                                                style={styles.smartSuggestions}
                                            >
                                                <View
                                                    style={
                                                        styles.suggestionHeader
                                                    }
                                                >
                                                    <MaterialIcons
                                                        name="lightbulb"
                                                        size={16}
                                                        color={theme.PRIMARY}
                                                    />
                                                    <Text
                                                        style={[
                                                            styles.suggestionTitle,
                                                            {
                                                                color: theme.PRIMARY,
                                                            },
                                                        ]}
                                                    >
                                                        AI Suggestion
                                                    </Text>
                                                </View>
                                                <Text
                                                    style={[
                                                        styles.suggestionText,
                                                        {
                                                            color: theme.TEXT_SECONDARY,
                                                        },
                                                    ]}
                                                >
                                                    {values.specialInstructions
                                                        .toLowerCase()
                                                        .includes('budget')
                                                        ? "💡 Consider mentioning your preferred payment schedule or if you're open to phased construction."
                                                        : values.specialInstructions
                                                                .toLowerCase()
                                                                .includes(
                                                                    'eco'
                                                                ) ||
                                                            values.specialInstructions
                                                                .toLowerCase()
                                                                .includes(
                                                                    'green'
                                                                )
                                                          ? '🌱 You might also want to specify interest in solar panels, rainwater harvesting, or LEED certification.'
                                                          : values.specialInstructions
                                                                  .toLowerCase()
                                                                  .includes(
                                                                      'access'
                                                                  )
                                                            ? '♿ Consider mentioning specific accessibility features like grab bars, non-slip surfaces, or visual aids.'
                                                            : '✨ Great details! You might also want to mention your preferred communication style with contractors.'}
                                                </Text>
                                            </View>
                                        )}
                                    </View>
                                </View>
                                <TouchableOpacity
                                    style={[
                                        styles.submitButton,
                                        {
                                            borderColor: theme.PRIMARY,
                                            opacity:
                                                createProjectMutation.isPending
                                                    ? 0.7
                                                    : 1,
                                        },
                                    ]}
                                    onPress={handleSubmit}
                                    disabled={createProjectMutation.isPending}
                                    activeOpacity={0.8}
                                >
                                    <LinearGradient
                                        colors={[
                                            theme.PRIMARY,
                                            theme.SECONDARY,
                                        ]}
                                        start={{ x: 0, y: 0 }}
                                        end={{ x: 1, y: 0 }}
                                        style={styles.submitButtonGradient}
                                    >
                                        <Text
                                            style={[
                                                styles.submitText,
                                                { color: theme.WHITE },
                                            ]}
                                        >
                                            {createProjectMutation.isPending
                                                ? 'Creating...'
                                                : 'Create Project'}
                                        </Text>
                                    </LinearGradient>
                                </TouchableOpacity>
                            </Animated.View>
                        </ScrollView>
                    )}
                </Formik>
                {createProjectMutation.isPending && (
                    <View style={styles.scanningOverlay}>
                        <View style={styles.scanningContainer}>
                            <ActivityIndicator
                                size="large"
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.scanningText,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                            >
                                Creating Project...
                            </Text>
                        </View>
                    </View>
                )}
            </SafeAreaView>
        </View>
    );
}

const styles = StyleSheet.create({
    container: { flex: 1 },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'center',
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: height * 0.1,
    },
    safeArea: { flex: 1 },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    scrollView: { flex: 1 },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        marginHorizontal: 20,
        alignSelf: 'center',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 14,
        marginBottom: 24,
        textAlign: 'center',
    },
    inputGroup: {
        marginBottom: 8,
    },
    label: {
        fontSize: 12,
        fontWeight: '500',
        marginBottom: 8,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 4,
        paddingHorizontal: 16,
        height: 56,
    },
    addressInputContainer: {
        height: 80,
    },
    inputIcon: {
        marginRight: 12,
    },
    clearButton: {
        padding: 8,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 14,
    },
    inputError: {
        borderColor: 'red',
    },
    optionsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
    },
    optionButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        marginRight: 8,
        marginBottom: 8,
    },
    optionText: {
        fontSize: 14,
        fontWeight: '500',
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 24,
        marginBottom: 16,
    },
    sectionTitle: {
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 8,
    },
    sectionTitleContainer: {
        flex: 1,
        marginLeft: 8,
    },
    sectionSubtitle: {
        fontSize: 12,
        marginTop: 2,
        lineHeight: 16,
    },
    row: {
        flexDirection: 'row',
    },
    errorText: { fontSize: 12, marginTop: 4 },
    submitButton: {
        borderRadius: 12,
        overflow: 'hidden',
        borderWidth: 2,
        marginTop: 32,
        marginBottom: 8,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    submitButtonGradient: {
        paddingVertical: 14,
        alignItems: 'center',
    },
    submitText: {
        fontSize: 16,
        fontWeight: 'bold',
    },
    switchContainer: {
        marginTop: 16,
    },
    switchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 12,
    },
    switchLabel: {
        fontSize: 14,
        fontWeight: '500',
    },
    switchLabelContainer: {
        flex: 1,
        marginRight: 12,
    },
    switchDescription: {
        fontSize: 12,
        marginTop: 2,
        lineHeight: 16,
    },
    textArea: {
        minHeight: 80,
        textAlignVertical: 'top',
        paddingTop: 12,
    },
    inputHelper: {
        fontSize: 12,
        marginTop: 4,
        marginBottom: 8,
        lineHeight: 16,
    },
    inputHelperRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 4,
        paddingHorizontal: 4,
    },
    inputHelperText: {
        fontSize: 12,
        marginLeft: 4,
        fontStyle: 'italic',
    },
    textAreaContainer: {
        alignItems: 'flex-start',
        paddingTop: 12,
        paddingBottom: 12,
    },
    errorIcon: {
        marginLeft: 8,
    },
    scanningOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    scanningContainer: {
        backgroundColor: 'white',
        borderRadius: 20,
        padding: 30,
        alignItems: 'center',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 10,
        elevation: 10,
    },
    scanningText: {
        fontSize: 16,
        fontWeight: '600',
        marginTop: 16,
        textAlign: 'center',
    },
    fabButton: {
        position: 'absolute',
        bottom: 30,
        right: 20,
        width: 56,
        height: 56,
        borderRadius: 28,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 8,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        zIndex: 1000,
    },
    completionStatus: {
        backgroundColor: 'rgba(42, 142, 158, 0.1)',
        borderRadius: 12,
        padding: 16,
        marginBottom: 20,
        borderWidth: 1,
        borderColor: 'rgba(42, 142, 158, 0.2)',
    },
    completionRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
    },
    completionText: {
        fontSize: 14,
        fontWeight: '500',
        marginLeft: 8,
        flex: 1,
    },
    completionHint: {
        fontSize: 12,
        fontStyle: 'italic',
        textAlign: 'center',
    },
    submitButtonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    suggestionsContainer: {
        marginTop: 8,
        marginBottom: 4,
    },
    suggestionsLabel: {
        fontSize: 12,
        fontWeight: '500',
        marginBottom: 6,
    },
    suggestionsRow: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 6,
    },
    suggestionChip: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        marginRight: 6,
        marginBottom: 4,
    },
    suggestionText: { fontSize: 12, fontWeight: '500' },
    tipsContainer: {
        marginTop: 4,
    },
    tipText: {
        fontSize: 11,
        lineHeight: 16,
        marginBottom: 2,
    },
    // Modern Design Styles
    modernSectionContainer: {
        borderRadius: 16,
        padding: 20,
        marginBottom: 24,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
    },
    modernSectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 16,
    },
    iconContainer: {
        width: 48,
        height: 48,
        borderRadius: 24,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    headerTextContainer: {
        flex: 1,
    },
    modernSectionTitle: {
        fontSize: 18,
        fontWeight: '700',
        marginBottom: 4,
    },
    modernSectionSubtitle: {
        fontSize: 14,
        lineHeight: 20,
    },
    progressContainer: {
        marginTop: 12,
    },
    progressTrack: {
        height: 4,
        backgroundColor: 'rgba(0,0,0,0.1)',
        borderRadius: 2,
        marginBottom: 8,
    },
    progressFill: {
        height: '100%',
        borderRadius: 2,
        transition: 'width 0.3s ease',
    },
    progressText: {
        fontSize: 12,
        textAlign: 'center',
        fontStyle: 'italic',
    },
    // Modern Card Styles
    modernCard: {
        borderRadius: 16,
        padding: 20,
        marginBottom: 20,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
    },
    cardHeaderLeft: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    cardIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    cardTitle: {
        fontSize: 16,
        fontWeight: '600',
        marginBottom: 2,
    },
    cardSubtitle: {
        fontSize: 13,
        lineHeight: 18,
    },
    badge: {
        minWidth: 28,
        height: 28,
        borderRadius: 14,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 8,
    },
    badgeText: {
        fontSize: 12,
        fontWeight: '600',
    },
    // Facilities Grid Styles
    facilitiesGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
        marginBottom: 20,
    },
    facilityCard: {
        width: '30%',
        minWidth: 100,
        aspectRatio: 1,
        borderRadius: 12,
        padding: 12,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
    },
    facilityIconContainer: {
        width: 32,
        height: 32,
        borderRadius: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 8,
    },
    facilityName: {
        fontSize: 11,
        fontWeight: '500',
        textAlign: 'center',
        marginBottom: 2,
    },
    facilityCategory: {
        fontSize: 9,
        textAlign: 'center',
    },
    checkmark: {
        position: 'absolute',
        top: 4,
        right: 4,
        width: 16,
        height: 16,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
    },
    customInputSection: {
        marginTop: 16,
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.1)',
    },
    customInputLabel: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 8,
    },
    customInputContainer: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        borderWidth: 1,
        borderRadius: 12,
        padding: 12,
        minHeight: 60,
    },
    customInputIcon: {
        marginRight: 8,
        marginTop: 2,
    },
    customInput: {
        flex: 1,
        fontSize: 14,
        lineHeight: 20,
        textAlignVertical: 'top',
    },
    characterCount: {
        fontSize: 11,
        textAlign: 'right',
        marginTop: 4,
    },
    // Special Instructions Styles
    promptsContainer: {
        marginBottom: 20,
    },
    promptsTitle: {
        fontSize: 14,
        fontWeight: '500',
        marginBottom: 12,
    },
    promptsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
    },
    promptCard: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        borderWidth: 1,
        marginBottom: 4,
    },
    promptText: {
        fontSize: 12,
        marginLeft: 6,
        fontWeight: '500',
    },
    textAreaSection: {
        marginTop: 8,
    },
    enhancedTextArea: {
        borderRadius: 12,
        borderWidth: 1,
        overflow: 'hidden',
    },
    textAreaHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderBottomWidth: 1,
        borderBottomColor: 'rgba(0,0,0,0.1)',
    },
    textAreaLabel: {
        fontSize: 12,
        marginLeft: 6,
        flex: 1,
    },
    enhancedInput: {
        padding: 12,
        fontSize: 14,
        lineHeight: 20,
        minHeight: 120,
        textAlignVertical: 'top',
    },
    smartSuggestions: {
        marginTop: 12,
        padding: 12,
        borderRadius: 8,
        backgroundColor: 'rgba(0,0,0,0.02)',
    },
    suggestionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 6,
    },
    suggestionTitle: {
        fontSize: 12,
        fontWeight: '600',
        marginLeft: 4,
    },
    suggestionText: {
        fontSize: 11,
        lineHeight: 16,
    },
    picker: {
        flex: 1,
        height: 56,
        fontSize: 16,
    },
});
