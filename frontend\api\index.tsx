import axios from 'axios';
import * as SecureStore from 'expo-secure-store';

export const publicAPIClient = axios.create({
    baseURL: 'http://192.168.1.33:8080',
});

export const privateAPIClient = axios.create({
    baseURL: 'http://192.168.1.33:8080',
});

privateAPIClient.interceptors.request.use(async (config) => {
    const token = await SecureStore.getItemAsync('accessToken');
    const sessionId = await SecureStore.getItemAsync('sessionId');
    if (token) config.headers.Authorization = `Bearer ${token}`;
    if (sessionId) config.headers.Session = sessionId;
    return config;
});

privateAPIClient.interceptors.response.use(
    (res) => res,
    async (error) => {
        const originalRequest = error.config;
        if (error.response?.status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            const sessionId = await SecureStore.getItemAsync('sessionId');

            try {
                const { data } = await publicAPIClient.post(
                    '/users/refresh',
                    null,
                    {
                        headers: {
                            Session: sessionId,
                        },
                    }
                );

                const { accessToken } = data;
                await SecureStore.setItemAsync('accessToken', accessToken);
                return privateAPIClient(originalRequest);
            } catch (e) {
                return Promise.reject(e);
            }
        }
        return Promise.reject(error);
    }
);

publicAPIClient.interceptors.request.use((config) => {
    return config;
});

// Function to check if user has valid credentials using refresh endpoint
export const checkExistingCredentials = async () => {
    try {
        const sessionId = await SecureStore.getItemAsync('sessionId');
        const accessToken = await SecureStore.getItemAsync('accessToken');

        // If no session ID or access token, user is not authenticated
        if (!sessionId || !accessToken) {
            return { isValid: false, user: null };
        }

        // Try to refresh the token to validate credentials
        const { data } = await publicAPIClient.post(
            '/users/refresh',
            null,
            {
                headers: {
                    Session: sessionId,
                },
            }
        );

        // If refresh is successful, update the access token and return success
        if (data.accessToken) {
            await SecureStore.setItemAsync('accessToken', data.accessToken);
            return { isValid: true, user: data.user || null };
        }

        return { isValid: false, user: null };
    } catch (error) {
        // If refresh fails, credentials are invalid
        console.log('Credential check failed:', error instanceof Error ? error.message : 'Unknown error');

        // Clear invalid credentials
        await SecureStore.deleteItemAsync('accessToken');
        await SecureStore.deleteItemAsync('sessionId');

        return { isValid: false, user: null };
    }
};
